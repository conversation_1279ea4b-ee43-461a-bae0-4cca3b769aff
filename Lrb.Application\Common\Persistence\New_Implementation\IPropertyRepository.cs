﻿using Lrb.Application.Property.Web;
using Lrb.Application.Property.Web.Dtos;
using Lrb.Application.Property.Web.Requests;

namespace Lrb.Application.Common.Persistence.New_Implementation
{
    public interface IPropertyRepository : IEFRepository<Domain.Entities.Property>
    {
        public Task<bool> AddAsync(Lrb.Domain.Entities.Property property);
        Task<(IEnumerable<Domain.Entities.Property>, int, int, int, int)> GetAllPropertiesForWebAsync(GetAllPropertyRequest filter, Guid? masterPropertyAttributeId, Guid? masterPropertyAmenityId, Guid? masterPropertyTypeId, List<Guid>? propertyDimensionIds, NumericAttributesDto numericAttributesDto, List<Guid>? userIds, bool showAllProperties, PropertyTypeBaseId propertyTypeIds, string? tenantId = null, List<CustomPropertyAttributeDto>? attributes = null);

        #region Property Repository New Implementation
        Task<(IEnumerable<Lrb.Domain.Entities.Property>,  int) > GetAllPropertiesForWebNewAsync(V2GetAllPropertyRequest filter, List<Guid>? propertyDimensionIds, NumericAttributesDto numericAttributesDto, List<Guid>? userIds, bool showAllProperties, string? tenantId = null, List<Guid>? propertyIds = null, List<CustomPropertyAttributeDto>? attribute=null);
        Task<int> GetAllPropertyCountForWebAsync(V2GetAllPropertyRequest filter, List<Guid>? propertyDimensionIds, NumericAttributesDto numericAttributesDto, List<Guid>? userIds, bool showAllProperties, string? tenantId = null);

        Task<PropertyCountDto> GetPropertyTopLevelCountAsync( PropertyTypeBaseId? propertyTypeIds, V2GetAllPropertyCountRequest filter, List<Guid>? propertyDimensionIds ,NumericAttributesDto numericAttributesDto, List<Guid>? userIds, bool showAllProperties, string? tenantId = null, List<Guid>? propertyIds = null, List<CustomPropertyAttributeDto>? attributes = null);
        Task<IEnumerable<Lrb.Domain.Entities.Property>> GetAllPropertiesExportForWebNewAsync(V2GetAllPropertyRequest filter, List<Guid>? propertyDimensionIds, NumericAttributesDto numericAttributesDto, List<Guid>? userIds, bool showAllProperties, string? tenantId = null, List<Guid>? propertyIds = null);
        #endregion
        #region Anonymous
        Task<(IEnumerable<Domain.Entities.Property>, int, int, int, int)> GetAllAnonymousPropertiesForWebAsync(GetAllPropertyAnonymousRequest filter, Guid? masterPropertyAttributeId, Guid? masterPropertyAmenityId, Guid? masterPropertyTypeId, List<Guid>? propertyDimensionIds, NumericAttributesDto numericAttributesDto, List<Guid>? userIds, bool showAllProperties, PropertyTypeBaseId propertyTypeIds, string? tenantId = null, List<CustomPropertyAttributeDto>? attributes = null);
        #endregion

        #region Listing Management
        Task<(IEnumerable<Lrb.Domain.Entities.Property>, int)> GetAllPropertiesListingAsync(GetAllPropertyForListingManagementRequest filter, List<Guid>? propertyDimensionIds, NumericAttributesDto numericAttributesDto, List<Guid>? userIds, PropertyTypeBaseId propertyTypeBaseId, bool showAllProperties, string? tenantId = null, List<Guid>? propertyIds = null, List<CustomPropertyAttributeDto>? attributes = null);
        Task<GetPropertyCountForListingManagementDto> GetAllPropertiesCountListingAsync(GetAllPropertyForListingManagementRequest filter, List<Guid>? propertyDimensionIds, NumericAttributesDto numericAttributesDto, List<Guid>? userIds, bool showAllProperties, string? tenantId = null, List<Guid>? propertyIds = null, PropertyTypeBaseId propertyTypeBaseId = null, List<CustomPropertyAttributeDto>? attributes = null);
        Task<GetPropertySecondLevelFilterCountForListingManagementDto> GetAllPropertiesCountListingManagementAsync(PropertyTypeBaseId? propertyTypeIds, GetAllPropertyForListingManagementRequest filter, List<Guid>? propertyDimensionIds, NumericAttributesDto numericAttributesDto, List<Guid>? userIds, bool showAllProperties, string? tenantId = null, List<Guid>? propertyIds = null, List<CustomPropertyAttributeDto>? attributes = null);
        Task<IEnumerable<Lrb.Domain.Entities.Property>> GetAllPropertiesListingExportAsync(GetAllPropertyForListingManagementRequest filter, List<Guid>? propertyDimensionIds, NumericAttributesDto numericAttributesDto, List<Guid>? userIds, PropertyTypeBaseId propertyTypeBaseId, bool showAllProperties, string? tenantId = null, List<Guid>? propertyIds = null);
        #endregion
        #region Mobile
        Task<IEnumerable<Domain.Entities.Property>> GetAllPropertiesForMobileListingManagementAsync(Lrb.Application.Property.Mobile.GetAllPropertyForListingManagementRequest filter,Guid? masterPropertyAttributeId,Guid? masterPropertyAmenityId,Guid? masterPropertyTypeId,List<Guid>? propertyDimensionIds,Lrb.Application.Property.Mobile.NumericAttributesDto numericAttributesDto, List<Guid>? userIds,bool showAllProperties,string? tenantId = null,List<Guid>? propertyIds = null, List<CustomPropertyAttributeDto>? attributes = null);
        Task<int> GetAllPropertiesForMobileListingManagementCountAsync(Lrb.Application.Property.Mobile.GetAllPropertyForListingManagementRequest filter, Guid? masterPropertyAttributeId, Guid? masterPropertyAmenityId, Guid? masterPropertyTypeId, List<Guid>? propertyDimensionIds, Lrb.Application.Property.Mobile.NumericAttributesDto numericAttributesDto, List<Guid>? userIds, bool showAllProperties, string? tenantId = null, List<Guid>? propertyIds = null, List<CustomPropertyAttributeDto>? attributes = null);
        Task<IEnumerable<Domain.Entities.Property>> GetAllPropertiesCountForMobileListingManagementAsync(Lrb.Application.Property.Mobile.GetPropertyTopLevelCountForListingManagementRequest filter, Guid? masterPropertyAttributeId, Guid? masterPropertyAmenityId, Guid? masterPropertyTypeId, List<Guid>? propertyDimensionIds, Lrb.Application.Property.Mobile.NumericAttributesDto numericAttributesDto, List<Guid>? userIds, bool showAllProperties, string? tenantId = null, List<Guid>? propertyIds = null, List<CustomPropertyAttributeDto>? attributes = null);
        Task<(IEnumerable<Lrb.Domain.Entities.Property>, int)> GetAllPropertiesForMobileAsync(Lrb.Application.Property.Mobile.GetAllPropertyRequest filter, Guid? masterPropertyAttributeId, Guid? masterPropertyAmenityId, Guid? masterPropertyTypeId, List<Guid>? propertyDimensionIds, Lrb.Application.Property.Mobile.NumericAttributesDto numericAttributesDto, List<Guid>? userIds, bool showAllProperties, string? tenantId = null, List<Guid>? propertyIds = null, List<CustomPropertyAttributeDto>? attributes = null);
        #endregion

        #region Custom Methods
        Task<Domain.Entities.Property?> GetPropertyByReferenceIdAsync(string refId, CancellationToken cancellationToken = default);
        #endregion
    }
}

